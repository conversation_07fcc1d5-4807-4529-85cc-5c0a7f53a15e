import { Heading } from "../../Component/Global/Heading";
import { MainWrapper } from "../../Layout/MainWrapper";
import { PaddingConatiner } from "../../Layout/PaddingConatiner";
import { Pressable, ScrollView, Switch, ToastAndroid, View } from "react-native";
import { PlainText } from "../../Component/Global/PlainText";
import { Dropdown } from "react-native-element-dropdown";
import {
  GetDownloadPath,
  GetFontSizeValue,
  GetPlaybackQuality,
  GetThemePreference,
  GetColorScheme,
  SetDownloadPath, 
  SetFontSizeValue,
  SetPlaybackQuality,
  SetThemePreference,
  SetColorScheme
} from "../../LocalStorage/AppSettings";
import { useEffect, useState } from "react";
import { SmallText } from "../../Component/Global/SmallText";
import { useTheme } from "@react-navigation/native";
import { getColorSchemeOptions, availableColors } from "../../Theme/colorSchemes";

export const SettingsPage = ({navigation}) => {
  const { colors } = useTheme();
  const [Font, setFont] = useState("");
  const [Playback, setPlayback] = useState("");
  const [Download, setDownload] = useState("");
  const [themePreference, setThemePreference] = useState("");
  const [colorScheme, setColorScheme] = useState("");
  
  const FontSize = [
    { value: 'Small' },
    { value: 'Medium' },
    { value: 'Large' },
  ];
  
  const PlaybackQuality = [
    { value: '12kbps' },
    { value: '48kbps' },
    { value: '96kbps' },
    { value: '160kbps' },
    { value: '320kbps' },
  ];
  
  const DownloadPath = [
    { value: 'Music' },
    { value: 'Downloads' },
  ];
  
  async function GetFontSize(){
    const data = await GetFontSizeValue();
    setFont(data);
  }
  
  async function GetPlayBack(){
    const data = await GetPlaybackQuality();
    setPlayback(data);
  }
  
  async function GetDownLoad(){
    const data = await GetDownloadPath();
    setDownload(data);
  }
  
  async function GetTheme(){
    const data = await GetThemePreference();
    setThemePreference(data);
  }
  
  async function GetColorSchemePreference(){
    const data = await GetColorScheme();
    setColorScheme(data);
  }
  


  async function SetDownLoad({ value }){
    await SetDownloadPath(value);
    ToastAndroid.showWithGravity(
      `Download path changed to ${value}`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  async function SetPlayBack({ value }){
    await SetPlaybackQuality(value);
    ToastAndroid.showWithGravity(
      `Playback quality changed to ${value}`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  async function SetFont({ value }){
    await SetFontSizeValue(value);
    ToastAndroid.showWithGravity(
      `Font size changed to ${value}`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  async function handleThemeToggle() {
    const newTheme = themePreference === 'dark' ? 'light' : 'dark';
    setThemePreference(newTheme);
    await SetThemePreference(newTheme);
    // Theme will be updated on app restart
    ToastAndroid.showWithGravity(
      `Theme changed to ${newTheme} mode. Please restart the app to see changes.`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  async function handleColorSchemeChange({ value }) {
    setColorScheme(value);
    // Save the color scheme preference to AsyncStorage
    await SetColorScheme(value);
    ToastAndroid.showWithGravity(
      `Color scheme changed to ${value}. Please restart the app to see changes.`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  // Handle toggle for custom colors
  async function handleCustomColorsToggle(value) {
    setCustomColorsEnabled(value);
    await SetCustomColorsEnabled(value);
    ToastAndroid.showWithGravity(
      value ? 'Custom colors enabled' : 'Using color scheme preset',
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  // Open color picker for a specific color type
  function openColorPicker(colorType) {
    setCurrentColorType(colorType);
    // Set the temp color based on the current color for that type
    switch (colorType) {
      case 'icon':
        setTempColor(iconColor);
        break;
      case 'text':
        setTempColor(textColor);
        break;
      case 'accent':
        setTempColor(accentColor);
        break;
    }
    setColorPickerVisible(true);
  }
  
  // Apply the selected color
  async function applySelectedColor() {
    switch (currentColorType) {
      case 'icon':
        setIconColor(tempColor);
        await SetIconColor(tempColor);
        break;
      case 'text':
        setTextColor(tempColor);
        await SetTextColor(tempColor);
        break;
      case 'accent':
        setAccentColor(tempColor);
        await SetAccentColor(tempColor);
        break;
    }
    
    ToastAndroid.showWithGravity(
      `${currentColorType.charAt(0).toUpperCase() + currentColorType.slice(1)} color updated. Restart app to see all changes.`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
    
    setColorPickerVisible(false);
  }
  
  // Select a predefined color
  async function selectPredefinedColor(color, colorType) {
    switch (colorType) {
      case 'icon':
        setIconColor(color);
        await SetIconColor(color);
        break;
      case 'text':
        setTextColor(color);
        await SetTextColor(color);
        break;
      case 'accent':
        setAccentColor(color);
        await SetAccentColor(color);
        break;
    }
    
    ToastAndroid.showWithGravity(
      `${colorType.charAt(0).toUpperCase() + colorType.slice(1)} color updated. Restart app to see all changes.`,
      ToastAndroid.SHORT,
      ToastAndroid.CENTER,
    );
  }
  
  useEffect(() => {
    GetFontSize();
    GetPlayBack();
    GetDownLoad();
    GetTheme();
    GetColorSchemePreference();
  }, []);
  
  return (
    <MainWrapper>
      <PaddingConatiner>
        <Heading text={"SETTINGS"}/>
        <ScrollView>
          <EachSettingsButton text={"Change Name"} OnPress={()=>{
            navigation.navigate("ChangeName");
          }}/>
          <EachSettingsButton text={"Select Languages"} OnPress={()=>{
            navigation.navigate("SelectLanguages");
          }}/>
          <EachDropDownWithLabel data={FontSize} text={"Font size"} placeholder={Font} OnChange={SetFont}/>
          <EachDropDownWithLabel data={PlaybackQuality} text={"Playback quality"} placeholder={Playback} OnChange={SetPlayBack}/>
          <EachDropDownWithLabel data={DownloadPath} text={"Download Path"} placeholder={Download} OnChange={SetDownLoad}/>
          <ThemeToggle themeMode={themePreference} onToggle={handleThemeToggle}/>
          <EachDropDownWithLabel data={getColorSchemeOptions()} text={"Color Scheme"} placeholder={colorScheme} OnChange={handleColorSchemeChange}/>
          
          {/* Custom Colors Section */}
          <View style={{
            backgroundColor: colors.settingsButtonBg,
            padding: 20,
            borderRadius: 10,
            marginBottom: 10,
          }}>
            <View style={{
              flexDirection: "row",
              justifyContent: "space-between",
              alignItems: "center",
              marginBottom: 15,
            }}>
              <PlainText text={"Custom Colors"} style={{ color: colors.text }}/>
              <Switch
                trackColor={{ false: '#767577', true: colors.primary }}
                thumbColor={customColorsEnabled ? '#f5dd4b' : '#f4f3f4'}
                ios_backgroundColor="#3e3e3e"
                onValueChange={handleCustomColorsToggle}
                value={customColorsEnabled}
              />
            </View>
            
            {customColorsEnabled && (
              <>
                {/* Icon Color */}
                <View style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 15,
                }}>
                  <PlainText text={"Icon Color"} style={{ color: colors.text }}/>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Pressable 
                      onPress={() => openColorPicker('icon')}
                      style={{
                        width: 30,
                        height: 30,
                        borderRadius: 15,
                        backgroundColor: iconColor,
                        borderWidth: 2,
                        borderColor: colors.text,
                        marginRight: 10,
                      }}
                    />
                    <View style={{ flexDirection: "row" }}>
                      {availableColors.slice(0, 5).map((color, index) => (
                        <Pressable
                          key={index}
                          style={{
                            width: 20,
                            height: 20,
                            borderRadius: 10,
                            backgroundColor: color.value,
                            marginHorizontal: 2,
                          }}
                          onPress={() => selectPredefinedColor(color.value, 'icon')}
                        />
                      ))}
                    </View>
                  </View>
                </View>
                
                {/* Text Color */}
                <View style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                  marginBottom: 15,
                }}>
                  <PlainText text={"Text Color"} style={{ color: colors.text }}/>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Pressable 
                      onPress={() => openColorPicker('text')}
                      style={{
                        width: 30,
                        height: 30,
                        borderRadius: 15,
                        backgroundColor: textColor,
                        borderWidth: 2,
                        borderColor: colors.text,
                        marginRight: 10,
                      }}
                    />
                    <View style={{ flexDirection: "row" }}>
                      {availableColors.slice(5, 10).map((color, index) => (
                        <Pressable
                          key={index}
                          style={{
                            width: 20,
                            height: 20,
                            borderRadius: 10,
                            backgroundColor: color.value,
                            marginHorizontal: 2,
                          }}
                          onPress={() => selectPredefinedColor(color.value, 'text')}
                        />
                      ))}
                    </View>
                  </View>
                </View>
                
                {/* Accent Color */}
                <View style={{
                  flexDirection: "row",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}>
                  <PlainText text={"Accent Color"} style={{ color: colors.text }}/>
                  <View style={{ flexDirection: "row", alignItems: "center" }}>
                    <Pressable 
                      onPress={() => openColorPicker('accent')}
                      style={{
                        width: 30,
                        height: 30,
                        borderRadius: 15,
                        backgroundColor: accentColor,
                        borderWidth: 2,
                        borderColor: colors.text,
                        marginRight: 10,
                      }}
                    />
                    <View style={{ flexDirection: "row" }}>
                      {availableColors.slice(10, 15).map((color, index) => (
                        <Pressable
                          key={index}
                          style={{
                            width: 20,
                            height: 20,
                            borderRadius: 10,
                            backgroundColor: color.value,
                            marginHorizontal: 2,
                          }}
                          onPress={() => selectPredefinedColor(color.value, 'accent')}
                        />
                      ))}
                    </View>
                  </View>
                </View>
              </>
            )}
          </View>
          
          <SmallText text={"*Note: If you change font size, change name, select languages, theme, or colors, please restart the app to see all changes."}/>
        </ScrollView>
        
        {/* Color Picker Modal */}
        <Modal
          animationType="slide"
          transparent={true}
          visible={colorPickerVisible}
          onRequestClose={() => setColorPickerVisible(false)}
        >
          <View style={{
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            backgroundColor: 'rgba(0, 0, 0, 0.7)',
          }}>
            <View style={{
              width: '90%',
              backgroundColor: colors.card,
              borderRadius: 10,
              padding: 20,
              alignItems: 'center',
              shadowColor: '#000',
              shadowOffset: {
                width: 0,
                height: 2,
              },
              shadowOpacity: 0.25,
              shadowRadius: 3.84,
              elevation: 5,
            }}>
              <PlainText 
                text={`Select ${currentColorType.charAt(0).toUpperCase() + currentColorType.slice(1)} Color`} 
                style={{ color: colors.text, fontSize: 18, fontWeight: 'bold', marginBottom: 15 }}
              />
              
              <View style={{ width: '100%', height: 220, marginBottom: 20 }}>
                <ColorPicker
                  onColorSelected={color => setTempColor(color)}
                  style={{flex: 1}}
                  defaultColor={tempColor}
                />
              </View>
              
              <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '100%' }}>
                <Pressable 
                  style={{
                    padding: 10,
                    borderRadius: 5,
                    width: '45%',
                    alignItems: 'center',
                    backgroundColor: '#555',
                  }}
                  onPress={() => setColorPickerVisible(false)}
                >
                  <PlainText text={"Cancel"} style={{ color: '#fff' }}/>
                </Pressable>
                
                <Pressable 
                  style={{
                    padding: 10,
                    borderRadius: 5,
                    width: '45%',
                    alignItems: 'center',
                    backgroundColor: colors.primary,
                  }}
                  onPress={applySelectedColor}
                >
                  <PlainText text={"Apply"} style={{ color: '#fff' }}/>
                </Pressable>
              </View>
            </View>
          </View>
        </Modal>
      </PaddingConatiner>
    </MainWrapper>
  );
}

function EachSettingsButton({text, OnPress}) {
  const { colors } = useTheme();
  return (
    <Pressable onPress={OnPress} style={{
      backgroundColor: colors.settingsButtonBg,
      padding: 20,
      borderRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      marginBottom: 10,
    }}>
      <PlainText text={text} style={{ color: colors.text }}/>
      <PlainText text={"→"} style={{ color: colors.text }}/>
    </Pressable>
  );
}

function ThemeToggle({themeMode, onToggle}) {
  const { colors } = useTheme();
  return (
    <Pressable style={{
      backgroundColor: colors.settingsButtonBg,
      padding: 20,
      borderRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 10,
    }}>
      <PlainText text={"App Theme"} style={{ color: colors.text }}/>
      <View style={{ flexDirection: 'row', alignItems: 'center' }}>
        <PlainText text={themeMode === 'light' ? 'Light' : 'Dark'} style={{ color: colors.text }}/>
        <Switch
          trackColor={{ false: '#767577', true: colors.primary }}
          thumbColor={themeMode === 'light' ? '#f5dd4b' : '#f4f3f4'}
          ios_backgroundColor="#3e3e3e"
          onValueChange={onToggle}
          value={themeMode === 'light'}
          style={{ marginLeft: 10 }}
        />
      </View>
    </Pressable>
  );
}

function EachDropDownWithLabel({data, text, placeholder, OnChange}){
  const { colors } = useTheme();
  return (
    <View style={{
      backgroundColor: colors.settingsButtonBg,
      padding: 20,
      borderRadius: 10,
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: 10,
    }}>
      <PlainText text={text} style={{ color: colors.text }}/>
      <Dropdown 
        placeholder={placeholder} 
        placeholderStyle={{
          color: colors.text,
        }} 
        itemTextStyle={{
          color: colors.dropdownText,
        }} 
        containerStyle={{
          backgroundColor: colors.dropdownBg,
          borderRadius: 5,
          borderWidth: 0,
        }} 
        style={{
          width: 120,
          backgroundColor: colors.dropdownBg,
          borderRadius: 5,
        }} 
        activeColor={colors.primary + '20'}
        data={data} 
        labelField="value" 
        valueField="value" 
        onChange={OnChange}
      />
    </View>
  );
}
