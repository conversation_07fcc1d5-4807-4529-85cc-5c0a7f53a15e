<p align="center">
  <img src="Images/Logo.jpg" alt="Orbit Logo" width="150" height="150">
</p>

<h1 align="center">🎵 ORBIT 🎵</h1>
<h3 align="center">Where Music Flows Without Boundaries</h3>

<p align="center">
  <a href="https://github.com/gauravxdev/Orbit/issues"><strong>Report Bug</strong></a>
  ·
  <a href="https://github.com/gauravxdev/Orbit/issues"><strong>Request Feature</strong></a>
</p>


<p align="center">
  <b>⚠️ DISCLAIMER: This project is ONLY for educational purposes ⚠️</b><br>
  This application serves as a demonstration of modern mobile development techniques and API integration.
</p>

<a href="https://ibb.co/PztFkfDX"><img src="https://i.ibb.co/S7J5S9fY/Neon-gradient-mobile-mockup-instagram-post-20250331-234406-0000.png" alt="Neon-gradient-mobile-mockup-instagram-post-20250331-234406-0000" border="0"></a>
## ✨ Experience the Magic of Orbit

Orbit isn't just another music app—it's a revolution in how you experience sound. Built with passion and cutting-edge technology, Orbit delivers a premium, uninterrupted listening experience that puts your musical journey first.

### 🚀 Key Features

- **♾️ Ad-Free Experience** — Immerse yourself in pure music without interruptions
- **🎧 High-Quality Streaming** — Crystal clear audio that respects the artist's vision
- **⚡ Blazing Fast Performance** — Optimized for speed with React Native architecture
- **💾 Download & Offline Access** — Take your music anywhere, even without internet
- **🌙 Elegant Dark Mode** — Easy on the eyes for your late-night sessions
- **🎛️ Advanced Audio Controls** — Fine-tune your listening experience
- **🎮 Background Playback** — Music continues while you use other apps
- **📱 Responsive Design** — Perfect experience on any device size
- **📦 Small App Size** — Doesn't bloat your device storage
- **🔄 Auto-Updates** — Always enjoy the latest features and fixes
- **🧩 Custom Playlists** — Create and manage your personal collections
- **💽 Local Music Support** — Play songs stored on your device
- **👆 Intuitive UI** — Beautiful animations and easy navigation
- **🎨 Dynamic Theming** — UI adapts to album art for an immersive experience
- **⏱️ Sleep Timer** — Drift off to your favorite tunes

## 🔍 Why Orbit?

In a world cluttered with subscription-based streaming services and ad-interrupted experiences, Orbit stands as a beacon of what music apps should be: **focused on the music, not monetization**. Every design decision, every line of code, serves to enhance your connection with the artists and songs you love.

## 🤝 Join Our Community

<p align="center">
  <a href="https://t.me/+k7vvHEZ5DK5kZmI1">
    <img src="https://private-user-images.githubusercontent.com/97950192/329830321-98d4e8b5-62c2-41ec-8d66-73d791181fca.png?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NDMyMjAxNDcsIm5iZiI6MTc0MzIxOTg0NywicGF0aCI6Ii85Nzk1MDE5Mi8zMjk4MzAzMjEtOThkNGU4YjUtNjJjMi00MWVjLThkNjYtNzNkNzkxMTgxZmNhLnBuZz9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTAzMjklMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwMzI5VDAzNDQwN1omWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPTNkMDliOWZmNDEyOGVmNTZlMmIyODE0ZDBmZTBiYjZhMGFhYTRlMTNlMTc3ZDQ3NDE3MTA4NWMzYTU1YmEzMDUmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.m46xT0QjTlHa9TWyAphnJnZJYzKb9-GCTxn-D9wc8UU" alt="Telegram Community" width="200">
  </a>
</p>

Become part of a growing community of music enthusiasts and developers! Share ideas, get help, and contribute to making Orbit even better.

## 🛠️ Built With Excellence

Orbit leverages the power of modern technology to deliver a seamless experience:

- **[React Native](https://reactnative.dev/)** — Cross-platform development with native performance
- **[React Native Track Player](https://rntp.dev/)** — Advanced audio playback capabilities
- **[React Native Reanimated](https://docs.swmansion.com/react-native-reanimated/)** — Fluid animations and interactions
- **[JioSavan Unofficial API (New)](https://jiosavan-api-with-playlist.vercel.app/)** — Rich music catalog access
- **[JioSavan Unofficial API (Legacy)](https://jio-savan-api-m39q.vercel.app/)** — Extended compatibility

## 🚀 Getting Started

Transform your device into a music powerhouse in minutes:

### Prerequisites

- Node.js (latest LTS version recommended)
- npm or yarn
- React Native development environment

### Quick Setup

```bash
# Clone this musical journey
git clone https://github.com/Ismartgaurav/Orbit.git

# Enter the melody
cd Orbit

# Install the harmonies
npm install
# or
yarn install

# Begin the symphony
npm run android
# or
yarn android
```

## 🗺️ Roadmap

We're constantly composing new features to enhance your experience. See our [open issues](https://github.com/Ismartgaurav/Orbit/issues) for a glimpse into the future of Orbit and to suggest your own melodies.

## 🤲 Contributing

Every great symphony needs multiple instruments. Your contributions make Orbit more harmonious:

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

We welcome all contributions, from code refinements to documentation improvements!

## 📜 License

Orbit is orchestrated under the MIT License. See [LICENSE](https://github.com/gauravxdev/Orbit/blob/main/LICENSE) for the full composition.


## ⚠️ Legal Harmony

```
Orbit does not possess or maintain any association with the songs and other content accessible through the app.
All songs and other content are the property of their respective owners and are safeguarded by copyright law.
Orbit holds no liability for any copyright infringement or other violations of intellectual property rights
that may arise from the use of the songs and other content accessible through the app.

THIS PROJECT IS CREATED SOLELY FOR EDUCATIONAL PURPOSES.

Orbit employs third-party plugins and assumes no responsibility for any harm or damage to the respective owners or
any other parties resulting from the utilization of the songs and other content through the third-party plugins.
By using the app, you consent to utilizing the songs and other content exclusively for personal,
non-commercial purposes and in accordance with all applicable laws and regulations.
```

<p align="center">🎵 Created with ❤️ by <a href="https://github.com/gauravxdev">gauravxdev</a> 🎵</p>
