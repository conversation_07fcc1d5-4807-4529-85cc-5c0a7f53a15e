import Context from "./Context";
import { useEffect, useState } from "react";
import { AppState } from "react-native";
import TrackPlayer, { Event, useTrackPlayerEvents } from "react-native-track-player";
import { getRecommendedSongs } from "../Api/Recommended";
import { AddSongsToQueue } from "../MusicPlayerFunctions";
import FormatArtist from "../Utils/FormatArtists";
import { Repeats } from "../Utils/Repeats";
import { SetQueueSongs } from "../LocalStorage/storeQueue";
import { EachSongMenuModal } from "../Component/Global/EachSongMenuModal";
import { CacheManager } from "../Utils/CacheManager";
import { HistoryManager } from "../Utils/HistoryManager";


const events = [
    Event.PlaybackActiveTrackChanged,
    Event.PlaybackError,
    Event.PlaybackState,
    Event.PlaybackProgressUpdated,
];
const ContextState = (props)=>{
    const [Index, setIndex] = useState(0);
    const [QueueIndex, setQueueIndex] = useState(0);
    const [currentPlaying, setCurrentPlaying]  = useState({})
    const [Repeat, setRepeat] = useState(Repeats.NoRepeat);
    const [Visible, setVisible] = useState({
        visible:false,
    });
    const [previousScreen, setPreviousScreen] = useState(null);
    // Dedicated state for music player navigation - won't be affected by general navigation
    const [musicPreviousScreen, setMusicPreviousScreen] = useState("");
    
    // Add state to track the current playlist information
    const [currentPlaylistData, setCurrentPlaylistData] = useState(null);
    
    // Add state to track liked playlists for UI updates
    const [likedPlaylists, setLikedPlaylists] = useState([]);

    // Track listening time for history
    const [currentTrackStartTime, setCurrentTrackStartTime] = useState(null);
    const [lastTrackedSong, setLastTrackedSong] = useState(null);
    const [lastProgressUpdate, setLastProgressUpdate] = useState(null);
    const [accumulatedListeningTime, setAccumulatedListeningTime] = useState(0);

    const [Queue, setQueue] = useState([]);
    async function updateTrack (){
        const tracks = await TrackPlayer.getQueue();
        // await SetQueueSongs(tracks)
        console.log(tracks);
        const ids = tracks.map((e)=>e.id)
        const queuesId = Queue.map((e)=>e.id)
        if (JSON.stringify(ids) !== JSON.stringify(queuesId)){
            setQueue(tracks)
        }
    }
    
    // Function to update liked playlists state and trigger UI updates
    function updateLikedPlaylist() {
        // This is just to trigger rerenders when playlists are liked/unliked
        setLikedPlaylists(prev => [...prev]);
    }
    
    async function AddRecommendedSongs(index,id){
        const tracks = await TrackPlayer.getQueue();
        const totalTracks = tracks.length - 1
        if (index >= totalTracks - 2){
           try {
               const songs = await getRecommendedSongs(id)
               if (songs?.data?.length !== 0){
                   const ForMusicPlayer = songs.data.map((e)=> {
                       return {
                           url:e.downloadUrl[3].url,
                           title:e.name.toString().replaceAll("&quot;","\"").replaceAll("&amp;","and").replaceAll("&#039;","'").replaceAll("&trade;","™"),
                           artist:FormatArtist(e?.artists?.primary).toString().replaceAll("&quot;","\"").replaceAll("&amp;","and").replaceAll("&#039;","'").replaceAll("&trade;","™"),
                           artwork:e.image[2].url,
                           duration:e.duration,
                           id:e.id,
                           language:e.language,
                       }
                   })
                   await AddSongsToQueue(ForMusicPlayer)
               }
           } catch (e) {
               console.log(e);
           } finally {
               await updateTrack()
           }
        }
    }

    useTrackPlayerEvents(events, async (event) => {
        if (event.type === Event.PlaybackError) {
            console.warn('An error occured while playing the current track.');
        }

        if (event.type === Event.PlaybackProgressUpdated) {
            // Track real-time listening progress
            if (lastTrackedSong && lastProgressUpdate) {
                const currentTime = Date.now();
                const timeDiff = (currentTime - lastProgressUpdate) / 1000;

                // Only track reasonable time differences
                if (timeDiff >= 0.5 && timeDiff <= 2) {
                    const newAccumulated = accumulatedListeningTime + timeDiff;
                    setAccumulatedListeningTime(newAccumulated);

                    // Save accumulated time every 5 seconds
                    if (newAccumulated >= 5) {
                        const success = await HistoryManager.trackListeningTime(lastTrackedSong.id, Math.floor(newAccumulated));
                        if (success) {
                            setAccumulatedListeningTime(0);
                        }
                    }
                }
            }
            setLastProgressUpdate(Date.now());
        }

        if (event.type === Event.PlaybackActiveTrackChanged) {
            // Track remaining listening time for previous song if it exists
            if (lastTrackedSong && accumulatedListeningTime > 0) {
                await HistoryManager.trackListeningTime(lastTrackedSong.id, Math.floor(accumulatedListeningTime));
                setAccumulatedListeningTime(0);
            }

            setCurrentPlaying(event.track);

            // Track new song play and start timing
            if (event.track?.id) {
                const songData = {
                    id: event.track.id,
                    title: event.track.title,
                    artist: event.track.artist,
                    artwork: event.track.artwork,
                    url: event.track.url
                };

                const trackSuccess = await HistoryManager.trackSongPlay(songData);
                if (trackSuccess) {
                    setLastTrackedSong(event.track);
                    setLastProgressUpdate(Date.now());
                    setAccumulatedListeningTime(0);
                    console.log(`🎵 Now tracking: "${event.track.title}"`);
                } else {
                    setLastTrackedSong(null);
                    setLastProgressUpdate(null);
                }

                if (Repeat === Repeats.NoRepeat){
                    AddRecommendedSongs(event.index, event.track.id);
                }
            }
        }

        if (event.type === Event.PlaybackState) {
            // Track time when playback is paused or stopped
            if (event.state === 'paused' || event.state === 'stopped') {
                if (lastTrackedSong && accumulatedListeningTime > 0) {
                    await HistoryManager.trackListeningTime(lastTrackedSong.id, Math.floor(accumulatedListeningTime));
                    setAccumulatedListeningTime(0);
                }
                setLastProgressUpdate(null);
            } else if (event.state === 'playing') {
                setLastProgressUpdate(Date.now());
            }
        }
    });
    async function InitialSetup(){
        try {
            // Clear old cache entries to prevent storage full errors
            await CacheManager.clearOldCacheEntries();
            
            await TrackPlayer.setupPlayer()
            console.log('Player initialized successfully in Context');
        } catch (error) {
            // Ignore the error if player is already initialized
            if (error.message && error.message.includes('player has already been initialized')) {
                console.log('Player already initialized in Context');
            } else {
                console.error('Error initializing player in Context:', error);
            }
        }
        
        await updateTrack()
        await getCurrentSong()
    }
    async function getCurrentSong(){
        const song = await TrackPlayer.getActiveTrack()
        setCurrentPlaying(song)
    }
    // Function to track final listening time when app is backgrounded or closed
    const trackFinalListeningTime = async () => {
        if (lastTrackedSong && accumulatedListeningTime > 0) {
            await HistoryManager.trackListeningTime(lastTrackedSong.id, Math.floor(accumulatedListeningTime));
            setAccumulatedListeningTime(0);
        }
    };

    useEffect(() => {
        InitialSetup();

        // Add app state change listener to track listening time when app goes to background
        const handleAppStateChange = (nextAppState) => {
            if (nextAppState === 'background' || nextAppState === 'inactive') {
                trackFinalListeningTime();
            }
        };

        const appStateSubscription = AppState.addEventListener('change', handleAppStateChange);

        return () => {
            // Cleanup when component unmounts
            trackFinalListeningTime();
            appStateSubscription?.remove();
        };
    }, []);
    return <Context.Provider value={{
        currentPlaying,  
        Repeat, 
        setRepeat, 
        updateTrack, 
        Index, 
        setIndex, 
        QueueIndex, 
        setQueueIndex, 
        setVisible, 
        Queue, 
        previousScreen, 
        setPreviousScreen,
        musicPreviousScreen,
        setMusicPreviousScreen,
        currentPlaylistData,
        setCurrentPlaylistData,
        updateLikedPlaylist,
        likedPlaylists
    }}>
        {props.children}
         <EachSongMenuModal setVisible={setVisible} Visible={Visible}/>
    </Context.Provider>
}

export default  ContextState
