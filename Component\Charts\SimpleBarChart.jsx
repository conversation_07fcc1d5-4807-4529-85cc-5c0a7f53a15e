import React from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Svg, { Rect, Text as SvgText, Line } from 'react-native-svg';
import { useTheme } from '@react-navigation/native';

const { width: screenWidth } = Dimensions.get('window');

export const SimpleBarChart = ({ 
  data = [], 
  height = 200, 
  showValues = true, 
  title = '',
  formatValue = (value) => value.toString()
}) => {
  const { colors, dark } = useTheme();
  
  if (!data || data.length === 0) {
    return (
      <View style={[styles.container, { height }]}>
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
        <View style={[styles.emptyContainer, { backgroundColor: dark ? '#1E1E1E' : '#F5F5F5' }]}>
          <Text style={[styles.emptyText, { color: colors.textSecondary }]}>
            No data available
          </Text>
        </View>
      </View>
    );
  }

  const chartWidth = screenWidth - 40; // Account for padding
  const chartHeight = height - 80; // Account for title and labels
  const barWidth = Math.max(20, (chartWidth - (data.length - 1) * 8) / data.length);
  const maxValue = Math.max(...data.map(item => item.value), 1);

  console.log('Chart data:', data);
  console.log('Max value:', maxValue);

  const getBarHeight = (value) => {
    if (maxValue === 0) return 0;
    const height = (value / maxValue) * (chartHeight - 40);
    return Math.max(height, value > 0 ? 2 : 0); // Minimum height of 2px for non-zero values
  };

  const getBarColor = (index) => {
    // Create a gradient of colors based on the theme
    const baseColor = colors.primary;
    const opacity = 0.7 + (index / data.length) * 0.3;
    return `${baseColor}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`;
  };

  return (
    <View style={[styles.container, { height }]}>
      {title && (
        <Text style={[styles.title, { color: colors.text }]}>{title}</Text>
      )}
      
      <View style={styles.chartContainer}>
        <Svg width={chartWidth} height={chartHeight}>
          {/* Grid lines */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio, index) => {
            const y = chartHeight - 20 - (ratio * (chartHeight - 40));
            return (
              <Line
                key={`grid-${index}`}
                x1="0"
                y1={y}
                x2={chartWidth}
                y2={y}
                stroke={dark ? '#333' : '#E0E0E0'}
                strokeWidth="1"
                opacity="0.5"
              />
            );
          })}
          
          {/* Bars */}
          {data.map((item, index) => {
            const barHeight = getBarHeight(item.value);
            const x = index * (barWidth + 8);
            const y = chartHeight - 20 - barHeight;
            
            return (
              <React.Fragment key={`bar-${index}`}>
                <Rect
                  x={x}
                  y={y}
                  width={barWidth}
                  height={barHeight}
                  fill={getBarColor(index)}
                  rx="2"
                />
                
                {/* Value labels on top of bars */}
                {showValues && item.value > 0 && (
                  <SvgText
                    x={x + barWidth / 2}
                    y={y - 5}
                    fontSize="10"
                    fill={colors.textSecondary}
                    textAnchor="middle"
                  >
                    {formatValue(item.value)}
                  </SvgText>
                )}
                
                {/* X-axis labels */}
                <SvgText
                  x={x + barWidth / 2}
                  y={chartHeight - 5}
                  fontSize="10"
                  fill={colors.textSecondary}
                  textAnchor="middle"
                >
                  {item.label}
                </SvgText>
              </React.Fragment>
            );
          })}
        </Svg>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    marginVertical: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    textAlign: 'center',
  },
  chartContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 8,
    marginTop: 8,
  },
  emptyText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
});
