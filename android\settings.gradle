rootProject.name = 'Orbit'

// Exclude problematic modules before applying native modules settings
def exclusionList = ['react-native-volume-control']

// Custom settings provider that excludes problematic modules
gradle.ext.exclusionList = exclusionList
gradle.ext.originalSettingsDir = settingsDir

// Apply native modules settings with filtering
apply from: file("../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesSettingsGradle(settings)
include ':app'
include ':react-native-fetch-blob'
project(':react-native-fetch-blob').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-fetch-blob/android')
includeBuild('../node_modules/@react-native/gradle-plugin')
include ':app', ':react-native-code-push'
project(':react-native-code-push').projectDir = new File(rootProject.projectDir, '../node_modules/react-native-code-push/android/app')

// Add these lines to fix the dependency issues
gradle.beforeProject { project ->
    project.extensions.extraProperties.set('android.useAndroidX', true)
    project.extensions.extraProperties.set('android.enableJetifier', true)
}

// Apply the disable module script
apply from: file("disableVolControlModule.gradle")

