import AsyncStorage from '@react-native-async-storage/async-storage';

const STORAGE_KEYS = {
  LISTENING_HISTORY: '@orbit_listening_history',
  DAILY_STATS: '@orbit_daily_stats',
};

// Get current date in YYYY-MM-DD format
const getCurrentDate = () => {
  return new Date().toISOString().split('T')[0];
};

// Get start of current week (Monday)
const getWeekStart = () => {
  const now = new Date();
  const dayOfWeek = now.getDay();
  const diff = now.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(now.setDate(diff));
  return monday.toISOString().split('T')[0];
};

// Track when a song starts playing
const trackSongPlay = async (songData) => {
  try {
    const {
      id,
      title,
      artist,
      artwork,
      duration,
      url,
      sourceType = 'online', // 'online', 'downloaded', 'local'
      isDownloaded = false,
      isLocal = false
    } = songData;

    if (!id || !title) {
      console.warn('Invalid song data for history tracking');
      return;
    }

    const currentDate = getCurrentDate();
    const timestamp = Date.now();

    // Get existing history
    const history = await getListeningHistory();
    
    // Initialize song entry if it doesn't exist
    if (!history[id]) {
      history[id] = {
        id,
        title,
        artist: artist || 'Unknown Artist',
        artwork: artwork || '',
        duration: duration || 0,
        url,
        sourceType,
        isDownloaded,
        isLocal,
        totalPlayCount: 0,
        totalListeningTime: 0, // in seconds
        firstPlayed: timestamp,
        lastPlayed: timestamp,
        dailyStats: {},
        playStartTime: timestamp
      };
    } else {
      // Update existing entry
      history[id].lastPlayed = timestamp;
      history[id].playStartTime = timestamp;
      history[id].sourceType = sourceType;
      history[id].isDownloaded = isDownloaded;
      history[id].isLocal = isLocal;
    }

    // Initialize daily stats for current date
    if (!history[id].dailyStats[currentDate]) {
      history[id].dailyStats[currentDate] = {
        playCount: 0,
        listeningTime: 0
      };
    }

    // Increment play count
    history[id].totalPlayCount++;
    history[id].dailyStats[currentDate].playCount++;

    // Save updated history
    await AsyncStorage.setItem(STORAGE_KEYS.LISTENING_HISTORY, JSON.stringify(history));

    console.log(`Tracked play for: ${title} by ${artist}`);
  } catch (error) {
    console.error('Error tracking song play:', error);
  }
};

// Track listening time when song ends or is paused
const trackListeningTime = async (songId, actualListeningTime) => {
  try {
    if (!songId || actualListeningTime <= 0) {
      console.log('Invalid listening time data:', { songId, actualListeningTime });
      return;
    }

    const currentDate = getCurrentDate();
    const history = await getListeningHistory();

    if (history[songId]) {
      // Add to total listening time
      history[songId].totalListeningTime += actualListeningTime;

      // Add to daily stats
      if (!history[songId].dailyStats[currentDate]) {
        history[songId].dailyStats[currentDate] = {
          playCount: 0,
          listeningTime: 0
        };
      }
      history[songId].dailyStats[currentDate].listeningTime += actualListeningTime;

      // Save updated history
      await AsyncStorage.setItem(STORAGE_KEYS.LISTENING_HISTORY, JSON.stringify(history));

      console.log(`Tracked ${actualListeningTime}s for "${history[songId].title}" on ${currentDate}`);
      console.log(`Total time for song: ${history[songId].totalListeningTime}s`);
    } else {
      console.warn('Song not found in history for time tracking:', songId);
    }
  } catch (error) {
    console.error('Error tracking listening time:', error);
  }
};

// Get all listening history
const getListeningHistory = async () => {
  try {
    const historyData = await AsyncStorage.getItem(STORAGE_KEYS.LISTENING_HISTORY);
    return historyData ? JSON.parse(historyData) : {};
  } catch (error) {
    console.error('Error getting listening history:', error);
    return {};
  }
};

// Get history sorted by various criteria
const getSortedHistory = async (sortBy = 'lastPlayed', limit = null) => {
  try {
    const history = await getListeningHistory();
    const historyArray = Object.values(history);

    let sortedHistory;
    switch (sortBy) {
      case 'playCount':
        sortedHistory = historyArray.sort((a, b) => b.totalPlayCount - a.totalPlayCount);
        break;
      case 'listeningTime':
        sortedHistory = historyArray.sort((a, b) => b.totalListeningTime - a.totalListeningTime);
        break;
      case 'firstPlayed':
        sortedHistory = historyArray.sort((a, b) => a.firstPlayed - b.firstPlayed);
        break;
      case 'lastPlayed':
      default:
        sortedHistory = historyArray.sort((a, b) => b.lastPlayed - a.lastPlayed);
        break;
    }

    return limit ? sortedHistory.slice(0, limit) : sortedHistory;
  } catch (error) {
    console.error('Error getting sorted history:', error);
    return [];
  }
};

// Get today's listening stats
const getTodayStats = async () => {
  try {
    const currentDate = getCurrentDate();
    const history = await getListeningHistory();
    
    let totalPlayCount = 0;
    let totalListeningTime = 0;
    let uniqueSongs = 0;

    Object.values(history).forEach(song => {
      if (song.dailyStats[currentDate]) {
        totalPlayCount += song.dailyStats[currentDate].playCount;
        totalListeningTime += song.dailyStats[currentDate].listeningTime;
        if (song.dailyStats[currentDate].playCount > 0) {
          uniqueSongs++;
        }
      }
    });

    return {
      playCount: totalPlayCount,
      listeningTime: totalListeningTime,
      uniqueSongs
    };
  } catch (error) {
    console.error('Error getting today stats:', error);
    return { playCount: 0, listeningTime: 0, uniqueSongs: 0 };
  }
};

// Get this week's listening stats
const getWeekStats = async () => {
  try {
    const today = new Date();
    const history = await getListeningHistory();

    let totalPlayCount = 0;
    let totalListeningTime = 0;
    let uniqueSongs = 0;
    const dailyData = {};

    // Initialize daily data for the current week (Monday to Sunday)
    const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Get Monday of current week

    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + mondayOffset + i);
      const dateStr = date.toISOString().split('T')[0];
      dailyData[dateStr] = { playCount: 0, listeningTime: 0 };
    }

    // Calculate week start for filtering
    const weekStartDate = new Date(today);
    weekStartDate.setDate(today.getDate() + mondayOffset);
    const weekStart = weekStartDate.toISOString().split('T')[0];

    console.log('Week calculation - Today:', today.toISOString().split('T')[0], 'Week start:', weekStart);
    console.log('Daily data initialized:', Object.keys(dailyData));

    Object.values(history).forEach(song => {
      let songPlayedThisWeek = false;

      if (song.dailyStats) {
        Object.keys(song.dailyStats).forEach(date => {
          if (date >= weekStart) {
            const dayStats = song.dailyStats[date];
            totalPlayCount += dayStats.playCount || 0;
            totalListeningTime += dayStats.listeningTime || 0;

            // Add to daily data if this date is in our week
            if (dailyData[date]) {
              dailyData[date].playCount += dayStats.playCount || 0;
              dailyData[date].listeningTime += dayStats.listeningTime || 0;
              console.log(`Added stats for ${date}:`, dayStats);
            }

            if ((dayStats.playCount || 0) > 0) {
              songPlayedThisWeek = true;
            }
          }
        });
      }

      if (songPlayedThisWeek) {
        uniqueSongs++;
      }
    });

    console.log('Week stats calculated:', { totalPlayCount, totalListeningTime, uniqueSongs });
    console.log('Final daily data:', dailyData);

    return {
      playCount: totalPlayCount,
      listeningTime: totalListeningTime,
      uniqueSongs,
      dailyData
    };
  } catch (error) {
    console.error('Error getting week stats:', error);
    return { playCount: 0, listeningTime: 0, uniqueSongs: 0, dailyData: {} };
  }
};

// Format time in seconds to readable format
const formatTime = (seconds) => {
  if (seconds < 60) {
    return `${Math.round(seconds)}s`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes}m`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  }
};

// Clear old history data (optional cleanup function)
const clearOldHistory = async (daysToKeep = 90) => {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    const cutoffTimestamp = cutoffDate.getTime();

    const history = await getListeningHistory();
    let cleaned = false;

    Object.keys(history).forEach(songId => {
      if (history[songId].lastPlayed < cutoffTimestamp) {
        delete history[songId];
        cleaned = true;
      } else {
        // Clean old daily stats
        Object.keys(history[songId].dailyStats).forEach(date => {
          if (new Date(date).getTime() < cutoffTimestamp) {
            delete history[songId].dailyStats[date];
            cleaned = true;
          }
        });
      }
    });

    if (cleaned) {
      await AsyncStorage.setItem(STORAGE_KEYS.LISTENING_HISTORY, JSON.stringify(history));
      console.log('Cleaned old history data');
    }
  } catch (error) {
    console.error('Error cleaning old history:', error);
  }
};

// Search history by query
const searchHistory = async (query) => {
  try {
    const history = await getListeningHistory();
    const historyArray = Object.values(history);

    if (!query || !query.trim()) {
      return historyArray.sort((a, b) => b.lastPlayed - a.lastPlayed);
    }

    const searchTerm = query.toLowerCase().trim();
    return historyArray.filter(song =>
      song.title.toLowerCase().includes(searchTerm) ||
      song.artist.toLowerCase().includes(searchTerm)
    ).sort((a, b) => b.lastPlayed - a.lastPlayed);
  } catch (error) {
    console.error('Error searching history:', error);
    return [];
  }
};

// Get total stats (all time)
const getTotalStats = async () => {
  try {
    const history = await getListeningHistory();

    let totalPlayCount = 0;
    let totalListeningTime = 0;
    let totalSongs = Object.keys(history).length;

    Object.values(history).forEach(song => {
      totalPlayCount += song.totalPlayCount;
      totalListeningTime += song.totalListeningTime;
    });

    return {
      playCount: totalPlayCount,
      listeningTime: totalListeningTime,
      totalSongs
    };
  } catch (error) {
    console.error('Error getting total stats:', error);
    return { playCount: 0, listeningTime: 0, totalSongs: 0 };
  }
};

export const HistoryManager = {
  trackSongPlay,
  trackListeningTime,
  getListeningHistory,
  getSortedHistory,
  getTodayStats,
  getWeekStats,
  getTotalStats,
  searchHistory,
  formatTime,
  clearOldHistory
};
