# Listening History Feature

## Overview
The Listening History feature tracks user's music listening behavior across all music sources (online streaming, downloaded songs, and local music files) and provides detailed statistics and insights.

## Features

### 📊 Statistics Dashboard
- **Today's Stats**: Shows plays and listening time for the current day
- **Weekly Stats**: Displays total plays and listening time for the current week
- **Visual Chart**: Bar chart showing daily listening time for the past week

### 🎵 Song History Table
- **Complete History**: All previously played songs with detailed stats
- **Play Count**: Number of times each song was played
- **Listening Time**: Total time spent listening to each song
- **Last Played**: When the song was last played
- **Source Type**: Whether the song is from online streaming, downloaded, or local files

### 🔍 Search & Sort
- **Search**: Find songs by title or artist name
- **Sort Options**:
  - Recent: Sort by last played (default)
  - Most Played: Sort by play count
  - Most Time: Sort by total listening time

### 🎮 Playback Controls
- **Play from History**: Tap any song to play it immediately
- **Three-dot Menu** with options:
  - Play Next: Add song to play next in queue
  - Add to Playlist: Add song to custom playlists
  - Download: Download online songs (only shown for non-downloaded online songs)

## Technical Implementation

### Files Created/Modified

#### New Files:
1. `Component/Library/HistoryScreen.jsx` - Main history screen component
2. `Component/Library/HistoryCard.jsx` - Individual song history card
3. `Component/Charts/SimpleBarChart.jsx` - SVG-based bar chart component
4. `Utils/HistoryManager.js` - History tracking and data management

#### Modified Files:
1. `Route/Library/LibraryRoute.jsx` - Added history screen to navigation
2. `Route/Library/Library.jsx` - Added history card to library main screen
3. `Context/ContextState.jsx` - Added history tracking to music playback events

### Data Storage
- Uses AsyncStorage for persistent data storage
- Tracks daily statistics for detailed insights
- Automatically cleans old data (keeps 90 days by default)

### History Tracking
- **Automatic Tracking**: Every song play is automatically tracked
- **Listening Time**: Tracks actual listening time (not just song duration)
- **Multi-source Support**: Works with online, downloaded, and local music
- **App State Handling**: Tracks listening time even when app goes to background

### Data Structure
```javascript
{
  songId: {
    id: "song_id",
    title: "Song Title",
    artist: "Artist Name",
    artwork: "artwork_url",
    duration: 180000,
    url: "song_url",
    sourceType: "online|downloaded|local",
    isDownloaded: boolean,
    isLocal: boolean,
    totalPlayCount: 15,
    totalListeningTime: 2700, // in seconds
    firstPlayed: timestamp,
    lastPlayed: timestamp,
    dailyStats: {
      "2024-01-15": {
        playCount: 3,
        listeningTime: 540
      }
    }
  }
}
```

## User Experience

### Navigation
- Access via Library → History
- Clean, intuitive interface matching app's design
- Proper back navigation handling

### Performance
- Efficient data loading with loading indicators
- Pull-to-refresh functionality
- Optimized for large datasets

### Visual Design
- Consistent with app's theme (dark/light mode support)
- Color-coded charts and statistics
- Clean, readable song cards with artwork

## Privacy & Data Management
- All data stored locally on device
- No external data transmission
- Automatic cleanup of old data
- User has full control over their listening history

## Future Enhancements
- Export history data
- More detailed analytics (genres, artists, etc.)
- Listening streaks and achievements
- Weekly/monthly reports
- Playlist generation based on history
