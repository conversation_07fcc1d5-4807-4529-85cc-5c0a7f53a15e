{"name": "Orbit", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@gorhom/bottom-sheet": "^4", "@react-native-async-storage/async-storage": "^1.22.3", "@react-native-clipboard/clipboard": "^1.13.2", "@react-native-community/netinfo": "^11.4.1", "@react-native-community/slider": "^4.5.0", "@react-native-firebase/analytics": "^21.12.3", "@react-native-firebase/app": "^21.12.3", "@react-navigation/bottom-tabs": "^6.5.14", "@react-navigation/native": "^6.1.12", "@react-navigation/native-stack": "^6.9.22", "@react-navigation/stack": "^6.3.23", "axios": "^1.6.7", "i": "^0.3.7", "jiosavan": "^1.0.4", "lodash": "^4.17.21", "npm": "^11.1.0", "react": "18.2.0", "react-native": "0.73.4", "react-native-blob-util": "^0.19.8", "react-native-bouncy-checkbox": "^3.0.7", "react-native-code-push": "^8.2.1", "react-native-color-picker": "^0.6.0", "react-native-device-info": "^10.13.1", "react-native-draggable-flatlist": "^4.0.1", "react-native-element-dropdown": "^2.10.2", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "^2.15.0", "react-native-get-music-files": "^2.2.4", "react-native-gif": "^1.0.3", "react-native-linear-gradient": "^2.8.3", "react-native-modal": "^13.0.1", "react-native-permissions": "^5.2.5", "react-native-popup-dialog": "^0.18.3", "react-native-progress": "^5.0.1", "react-native-reanimated": "^3.7.2", "react-native-safe-area-context": "^4.9.0", "react-native-screens": "^3.29.0", "react-native-svg": "^15.11.2", "react-native-system-setting": "^1.7.6", "react-native-track-player": "^4.0.1", "react-native-vector-icons": "^10.2.0", "react-native-volume-manager": "^2.0.8", "react-redux": "^9.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.73.21", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.5", "@react-native/typescript-config": "0.73.1", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}