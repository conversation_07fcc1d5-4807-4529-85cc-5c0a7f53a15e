import React, { useState, useEffect } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  Text,
  Dimensions,
  RefreshControl,
  TextInput,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme, useNavigation, useFocusEffect } from '@react-navigation/native';
import React, { useCallback } from 'react';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { PlainText } from '../Global/PlainText';
import { HistoryCard } from './HistoryCard';
import { SimpleBarChart } from '../Charts/SimpleBarChart';
import { HistoryManager } from '../../Utils/HistoryManager';
import BackHandler from 'react-native/Libraries/Utilities/BackHandler';
import AsyncStorage from '@react-native-async-storage/async-storage';

const { width, height } = Dimensions.get('window');

export default function HistoryScreen(props) {
  const { colors, dark } = useTheme();
  const styles = getStyles(colors, dark);
  const navigation = useNavigation();
  
  const [historyData, setHistoryData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [todayStats, setTodayStats] = useState({ playCount: 0, listeningTime: 0, uniqueSongs: 0 });
  const [weekStats, setWeekStats] = useState({ playCount: 0, listeningTime: 0, uniqueSongs: 0, dailyData: {} });
  const [sortBy, setSortBy] = useState('lastPlayed'); // 'lastPlayed', 'playCount', 'listeningTime'
  const [error, setError] = useState(null);

  useEffect(() => {
    loadHistoryData();

    // Add back handler
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (showSearch) {
        setShowSearch(false);
        setSearchQuery('');
        return true;
      }

      // Navigate back to Library
      AsyncStorage.getItem('came_from_fullscreen_player')
        .then(value => {
          if (value === 'true') {
            AsyncStorage.removeItem('came_from_fullscreen_player');
            navigation.navigate('Library', {
              screen: 'LibraryPage',
              params: { timestamp: Date.now() }
            });
          } else {
            navigation.navigate('Library', { screen: 'LibraryPage' });
          }
        })
        .catch(() => {
          navigation.navigate('Library', { screen: 'LibraryPage' });
        });

      return true;
    });

    // Set up periodic refresh to update stats in real-time
    const refreshInterval = setInterval(() => {
      if (!isLoading && !refreshing) {
        loadHistoryData();
      }
    }, 30000); // Refresh every 30 seconds

    return () => {
      backHandler.remove();
      clearInterval(refreshInterval);
    };
  }, [navigation, showSearch, isLoading, refreshing]);

  // Refresh data when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      loadHistoryData();
    }, [])
  );

  // Filter data based on search query
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredData(historyData);
    } else {
      const query = searchQuery.toLowerCase().trim();
      const filtered = historyData.filter(song => 
        (song.title && song.title.toLowerCase().includes(query)) || 
        (song.artist && song.artist.toLowerCase().includes(query))
      );
      setFilteredData(filtered);
    }
  }, [searchQuery, historyData]);

  const loadHistoryData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load history data
      const history = await HistoryManager.getSortedHistory(sortBy);
      setHistoryData(history);

      // Load stats
      const todayData = await HistoryManager.getTodayStats();
      const weekData = await HistoryManager.getWeekStats();

      setTodayStats(todayData);
      setWeekStats(weekData);

      // Clean old history data periodically (every 30 days)
      const lastCleanup = await AsyncStorage.getItem('@orbit_last_history_cleanup');
      const now = Date.now();
      const thirtyDaysAgo = now - (30 * 24 * 60 * 60 * 1000);

      if (!lastCleanup || parseInt(lastCleanup) < thirtyDaysAgo) {
        await HistoryManager.clearOldHistory(90); // Keep 90 days of history
        await AsyncStorage.setItem('@orbit_last_history_cleanup', now.toString());
      }

    } catch (error) {
      console.error('Error loading history data:', error);
      setError('Failed to load listening history');
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHistoryData();
    setRefreshing(false);
  };

  const handleSortChange = async (newSortBy) => {
    if (newSortBy !== sortBy) {
      setSortBy(newSortBy);
      setIsLoading(true);
      const sortedHistory = await HistoryManager.getSortedHistory(newSortBy);
      setHistoryData(sortedHistory);
      setIsLoading(false);
    }
  };

  const getChartData = () => {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    const today = new Date();

    return days.map((day, index) => {
      // Calculate the date for each day of the current week
      const date = new Date(today);
      const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
      const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Get Monday of current week
      date.setDate(today.getDate() + mondayOffset + index);

      const dateStr = date.toISOString().split('T')[0];

      console.log(`Chart data for ${day} (${dateStr}):`, weekStats.dailyData[dateStr]);

      const dayData = weekStats.dailyData && weekStats.dailyData[dateStr]
        ? weekStats.dailyData[dateStr]
        : { listeningTime: 0 };

      const minutes = Math.round(dayData.listeningTime / 60);

      return {
        label: day,
        value: minutes,
        date: dateStr
      };
    });
  };

  const renderStatsCards = () => (
    <View style={styles.statsContainer}>
      <View style={styles.statsCard}>
        <Text style={styles.statsNumber}>{todayStats.playCount}</Text>
        <Text style={styles.statsLabel}>Plays Today</Text>
      </View>
      <View style={styles.statsCard}>
        <Text style={styles.statsNumber}>{weekStats.playCount}</Text>
        <Text style={styles.statsLabel}>This Week</Text>
      </View>
      <View style={styles.statsCard}>
        <Text style={styles.statsNumber}>{HistoryManager.formatTime(todayStats.listeningTime)}</Text>
        <Text style={styles.statsLabel}>Today's Time</Text>
      </View>
    </View>
  );

  const renderSortButtons = () => (
    <View style={styles.sortContainer}>
      <TouchableOpacity
        style={[styles.sortButton, sortBy === 'lastPlayed' && styles.sortButtonActive]}
        onPress={() => handleSortChange('lastPlayed')}
      >
        <Text style={[styles.sortButtonText, sortBy === 'lastPlayed' && styles.sortButtonTextActive]}>
          Recent
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.sortButton, sortBy === 'playCount' && styles.sortButtonActive]}
        onPress={() => handleSortChange('playCount')}
      >
        <Text style={[styles.sortButtonText, sortBy === 'playCount' && styles.sortButtonTextActive]}>
          Most Played
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.sortButton, sortBy === 'listeningTime' && styles.sortButtonActive]}
        onPress={() => handleSortChange('listeningTime')}
      >
        <Text style={[styles.sortButtonText, sortBy === 'listeningTime' && styles.sortButtonTextActive]}>
          Most Time
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderHeader = () => (
    <View>
      {renderStatsCards()}
      <SimpleBarChart
        data={getChartData()}
        height={180}
        title="This Week's Listening Time (minutes)"
        formatValue={(value) => `${value}m`}
      />
      {renderSortButtons()}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <PlainText text="Listening History" style={styles.title} />
        
        {showSearch ? (
          <View style={styles.searchBarContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="Search history..."
              placeholderTextColor={colors.placeholder}
              value={searchQuery}
              onChangeText={setSearchQuery}
              returnKeyType="search"
              autoCapitalize="none"
              selectionColor={colors.primary}
              autoFocus={true}
            />
            <TouchableOpacity onPress={() => {
              setShowSearch(false);
              setSearchQuery('');
            }}>
              <MaterialIcons name="close" size={24} color={colors.textSecondary} />
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity onPress={() => setShowSearch(true)} style={styles.searchIcon}>
            <MaterialIcons name="search" size={24} color={colors.text} />
          </TouchableOpacity>
        )}
      </View>
      
      <FlatList
        data={filteredData}
        renderItem={({ item, index }) => (
          <HistoryCard
            song={item}
            index={index}
            allSongs={filteredData}
            onRefresh={loadHistoryData}
          />
        )}
        keyExtractor={(item, index) => item.id ? item.id.toString() : index.toString()}
        ListHeaderComponent={!searchQuery ? renderHeader : null}
        ListEmptyComponent={
          isLoading ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={colors.primary} />
              <Text style={[styles.loadingText, { color: colors.textSecondary }]}>
                Loading history...
              </Text>
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <MaterialCommunityIcons name="history" size={50} color={colors.textSecondary} />
              <Text style={styles.emptyText}>
                {searchQuery
                  ? `No history matching "${searchQuery}"`
                  : "No listening history yet"}
              </Text>
              <Text style={styles.emptySubText}>
                {searchQuery
                  ? "Try a different search term"
                  : "Start playing some music to see your history"}
              </Text>
            </View>
          )
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[colors.primary]}
            tintColor={colors.primary}
          />
        }
      />
    </SafeAreaView>
  );
}

const getStyles = (colors, dark) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: dark ? '#121212' : '#FFFFFF',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: dark ? 'rgba(255,255,255,0.1)' : 'rgba(0,0,0,0.1)',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    color: colors.text,
  },
  searchIcon: {
    padding: 4,
  },
  searchBarContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: dark ? '#242424' : '#EFEFEF',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginLeft: 16,
    height: 40,
  },
  searchInput: {
    flex: 1,
    height: 40,
    color: colors.text,
    fontSize: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
  statsCard: {
    alignItems: 'center',
    backgroundColor: dark ? '#1A1A1A' : '#F5F5F5',
    borderRadius: 12,
    padding: 16,
    minWidth: width * 0.25,
  },
  statsNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 4,
  },
  statsLabel: {
    fontSize: 12,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  sortContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 8,
    justifyContent: 'space-around',
  },
  sortButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: dark ? '#2A2A2A' : '#E0E0E0',
  },
  sortButtonActive: {
    backgroundColor: colors.primary,
  },
  sortButtonText: {
    fontSize: 14,
    color: colors.textSecondary,
  },
  sortButtonTextActive: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  listContainer: {
    paddingBottom: 150,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    height: height * 0.4,
  },
  emptyText: {
    fontSize: 18,
    color: colors.text,
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 32,
    height: height * 0.3,
  },
  loadingText: {
    fontSize: 16,
    marginTop: 16,
  },
});
