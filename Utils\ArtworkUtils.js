import { safeExists } from './FileUtils';

/**
 * Reusable artwork utility for handling different types of artwork sources
 * Provides consistent artwork resolution across the app
 */

/**
 * Gets the appropriate artwork source for a track
 * @param {Object} track - Track object with artwork information
 * @param {Object} options - Additional options
 * @param {Function} options.getGifSource - Function to get GIF source for mymusic tracks
 * @param {boolean} options.preferHighQuality - Whether to prefer high quality images (default: true)
 * @returns {Object|number} - Image source object or require() result
 */
export const getArtworkSource = (track, options = {}) => {
  const { getGifSource, preferHighQuality = true } = options;
  
  if (!track) {
    return require('../Images/Music.jpeg');
  }

  // Handle mymusic tracks with GIF animations
  if (track.sourceType === 'mymusic' && getGifSource) {
    return getGifSource();
  }

  // Handle downloaded songs with local artwork files
  if (track.isDownloaded || track.isLocal) {
    // Check for local artwork path
    if (track.localArtworkPath) {
      return { uri: `file://${track.localArtworkPath}` };
    }
    
    // Check for file:// artwork URLs
    if (track.artwork && typeof track.artwork === 'string' && track.artwork.startsWith('file://')) {
      return { uri: track.artwork };
    }
  }

  // Handle online tracks: Prefer image array for quality selection
  if (track.image && Array.isArray(track.image) && track.image.length > 0) {
    let imageUrl = null;
    
    if (preferHighQuality) {
      // Try to find highest quality image
      const highQualityImage = track.image.find(i => i.quality === '500x500') ||
                              track.image[track.image.length - 1]; // Last item is usually highest quality
      
      if (highQualityImage?.url && isValidUrl(highQualityImage.url)) {
        imageUrl = highQualityImage.url;
      }
    } else {
      // Use first available image
      const firstImage = track.image.find(i => i.url && isValidUrl(i.url));
      if (firstImage) {
        imageUrl = firstImage.url;
      }
    }

    if (imageUrl) {
      return { uri: imageUrl };
    }
  }

  // Fallback to track.artwork if track.image array didn't yield a URL
  if (track.artwork && typeof track.artwork === 'string' && track.artwork !== 'null' && track.artwork.trim() !== '') {
    if (isValidUrl(track.artwork)) {
      let artworkUrl = track.artwork;
      
      if (preferHighQuality) {
        // Upgrade artwork URL to higher resolution if possible
        artworkUrl = upgradeArtworkQuality(artworkUrl);
      }
      
      return { uri: artworkUrl };
    }
  }
  
  // Final fallback
  return require('../Images/Music.jpeg');
};

/**
 * Checks if a URL is valid (http/https)
 * @param {string} url - URL to validate
 * @returns {boolean} - Whether the URL is valid
 */
const isValidUrl = (url) => {
  return typeof url === 'string' && (url.startsWith('http://') || url.startsWith('https://'));
};

/**
 * Upgrades artwork URL to higher quality if possible
 * @param {string} artworkUrl - Original artwork URL
 * @returns {string} - Upgraded artwork URL
 */
const upgradeArtworkQuality = (artworkUrl) => {
  // Handle JioSaavn CDN URLs
  if (artworkUrl.includes('saavncdn.com')) {
    return artworkUrl.replace(/50x50|150x150|500x500/g, '500x500');
  }
  
  // Handle other CDNs with quality parameters
  try {
    const url = new URL(artworkUrl);
    url.searchParams.set('quality', '100');
    return url.toString();
  } catch (e) {
    // If URL parsing fails, try direct string manipulation
    if (artworkUrl.includes('?')) {
      return `${artworkUrl}&quality=100`;
    } else {
      return `${artworkUrl}?quality=100`;
    }
  }
};

/**
 * Gets artwork source specifically for local/downloaded tracks
 * @param {Object} track - Track object
 * @returns {Object|number} - Image source
 */
export const getLocalArtworkSource = (track) => {
  if (!track) {
    return require('../Images/Music.jpeg');
  }

  // For downloaded songs, check local artwork first
  if (track.localArtworkPath) {
    return { uri: `file://${track.localArtworkPath}` };
  }
  
  if (track.artwork && typeof track.artwork === 'string' && track.artwork.startsWith('file://')) {
    return { uri: track.artwork };
  }

  // For mymusic tracks, use GIF placeholder
  if (track.sourceType === 'mymusic' || track.isLocal) {
    return require('../Images/a.gif');
  }

  // Fallback
  return require('../Images/Music.jpeg');
};

/**
 * Validates if a local artwork file exists
 * @param {string} artworkPath - Path to artwork file
 * @returns {Promise<boolean>} - Whether the file exists
 */
export const validateLocalArtwork = async (artworkPath) => {
  if (!artworkPath || typeof artworkPath !== 'string') {
    return false;
  }
  
  // Remove file:// prefix if present
  const cleanPath = artworkPath.replace('file://', '');
  
  try {
    return await safeExists(cleanPath);
  } catch (error) {
    console.warn('Error validating local artwork:', error);
    return false;
  }
};

/**
 * Gets the best available artwork source with fallback handling
 * @param {Object} track - Track object
 * @param {Object} options - Options
 * @returns {Promise<Object|number>} - Best available artwork source
 */
export const getBestArtworkSource = async (track, options = {}) => {
  const source = getArtworkSource(track, options);
  
  // If it's a local file, validate it exists
  if (source?.uri?.startsWith('file://')) {
    const exists = await validateLocalArtwork(source.uri);
    if (!exists) {
      console.warn(`Local artwork not found: ${source.uri}, using fallback`);
      return require('../Images/Music.jpeg');
    }
  }
  
  return source;
};
